# File List Management - Executive Summary

## Current Architecture

The `FileManager` class currently handles multiple responsibilities:

1. **UI Interaction**: File/folder selection dialogs
2. **Data Management**: Maintains canonical `file_paths_list`
3. **Integration**: Folder monitoring, file enrichment
4. **Communication**: Uses both direct interface calls and events

While functional after recent fixes, this design creates complexity and potential inconsistencies.

## Key Issues

1. **Mixed Responsibilities**: FileManager handles both UI interaction and data management
2. **Dual Communication**: Uses both events and direct interface calls
3. **Potential Inconsistency**: Between stored list and UI display
4. **Interface Bypass**: Direct calls to view.center_panel.set_files() bypass the interface abstraction

## Recommended Solution: State-Based Approach

Move file list management to the `StateManager` while keeping file selection UI interactions in `FileManager`.

### Benefits

- **Reduces Complexity**: FileManager focuses on UI interactions
- **Single Source of Truth**: State holds the canonical file list
- **Minimal Refactoring**: Works with existing architecture
- **Consistent Communication**: Standardizes on state-based updates

### Implementation (6-10 hours)

1. Move `file_paths_list` from FileManager to StateManager's state
2. Update FileManager to modify state instead of its own list
3. Ensure view subscribes to state changes for updates
4. Remove direct calls to view components in favor of interface methods

## Alternative Options

### Interface-First Approach
- Standardize on interface methods for all updates
- Add `set_file_list()` to view interface
- Replace events with direct interface calls

### Dedicated FileListManager
- Create specialized component for file list operations
- Clear separation between UI interaction and data management
- More significant refactoring required

## Architectural Alignment

The recommended approach aligns with established architectural principles:

1. **Interface Methods First**: Primary means of presenter-view communication
2. **Events for Specific Cases**: Multiple listeners, async operations
3. **Clean Separation**: No Qt dependencies in presenter layer
4. **Single Responsibility**: Components with focused concerns

## Conclusion

The State-Based Approach offers the best balance of architectural improvement and implementation simplicity. It addresses the current complexity in FileManager while maintaining compatibility with the existing codebase and architectural principles.
