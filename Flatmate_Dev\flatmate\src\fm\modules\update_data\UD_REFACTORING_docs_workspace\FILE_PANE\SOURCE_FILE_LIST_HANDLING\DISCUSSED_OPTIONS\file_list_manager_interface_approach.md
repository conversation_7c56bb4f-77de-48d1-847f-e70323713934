# Interface-Respecting FileListManager Approach

## Concept Overview

This approach creates a dedicated `FileListManager` while fully respecting the view interface and event architecture:

1. Create a specialized `FileListManager` that encapsulates file list operations
2. Maintain proper separation from Qt widgets through the view interface
3. Use the event system for communication between components
4. Integrate cleanly with the existing architecture

## Key Principles

- Respect the view interface (I_view) - no direct widget access
- Use the event system properly for communication
- Maintain separation of concerns
- Keep file list management focused and clean

## Implementation Design

### 1. Enhanced View Interface (I_view)

First, ensure the view interface has the necessary methods for file operations:

```python
class IUpdateDataView(Protocol):
    """Interface for Update Data View."""
    
    # Existing signals
    cancel_clicked = Signal()
    source_select_requested = Signal(str)
    save_select_requested = Signal()
    source_option_changed = Signal(str)
    save_option_changed = Signal(str)
    process_clicked = Signal()
    
    # Add file operation signals if not already present
    file_removed = Signal(str)  # Signal when user removes a file
    folder_monitoring_toggled = Signal(str, bool)  # Signal when user toggles folder monitoring
    
    # Existing methods
    def show_files_dialog(self, title: str, initial_dir: str) -> List[str]:
        """Show file selection dialog."""
        ...
    
    def show_folder_dialog(self, title: str, initial_dir: str) -> str:
        """Show folder selection dialog."""
        ...
    
    def update_files_display(self, event_data: Dict[str, Any]) -> None:
        """Update the files display with the given files."""
        ...
    
    def display_enriched_file_info(self, file_info_list: List[Dict[str, Any]]) -> None:
        """Display enriched file information."""
        ...
    
    def get_current_files(self) -> List[str]:
        """Get the current list of files from the view."""
        ...
```

### 2. FileListManager Class

```python
class FileListManager:
    """
    Manages the canonical list of files for processing.
    
    Responsibilities:
    - Maintains the canonical list of files
    - Tracks folders that have been added
    - Handles file/folder addition and removal
    - Manages folder monitoring state
    - Emits events for view updates
    """
    
    def __init__(self, local_bus, folder_monitor_service, file_info_service):
        """
        Initialize the file list manager.
        
        Args:
            local_bus: Local event bus for module events
            folder_monitor_service: Service for folder monitoring
            file_info_service: Service for enriching file information
        """
        self.local_bus = local_bus
        self.folder_monitor_service = folder_monitor_service
        self.file_info_service = file_info_service
        
        # Core data structures
        self.file_paths_list = []  # Canonical list of files
        self.folder_files_map = {}  # {folder_path: set(file_paths)}
        self.monitored_folders = set()  # Set of folders being monitored
        
        # Subscribe to events
        self._subscribe_to_events()
    
    def _subscribe_to_events(self):
        """Subscribe to relevant events."""
        # Subscribe to file removal events
        self.local_bus.subscribe(ViewEvents.FILE_REMOVED.value, self.handle_file_removed)
        
        # Subscribe to folder monitoring events
        self.local_bus.subscribe(ViewEvents.FOLDER_MONITORING_TOGGLED.value, self.handle_folder_monitoring_toggled)
        
        # Subscribe to folder monitor service events
        self.folder_monitor_service.file_discovered.connect(self.handle_file_discovered)
    
    def set_files(self, files: list, source_path: str = ""):
        """
        Set the list of files and emit update event.
        
        Args:
            files: List of file paths
            source_path: Source directory for relative paths
        """
        # Update canonical list
        self.file_paths_list = files
        
        # Update folder mapping
        self._track_files_by_folder(files)
        
        # Enrich file info
        enriched_info = self.enrich_file_info(files)
        
        # Emit event for view update
        self.local_bus.emit(ViewEvents.FILE_DISPLAY_UPDATED.value,
                          FileDisplayUpdateEvent(files=files, source_path=source_path, 
                                               enriched_info=enriched_info))
    
    def _track_files_by_folder(self, files: list):
        """
        Group files by their parent folders.
        
        Args:
            files: List of file paths
        """
        # Reset folder mapping if this is a new selection
        if not set(files).issubset(set(self.file_paths_list)):
            self.folder_files_map = {}
            
        # Build folder mapping
        for file_path in files:
            folder = str(Path(file_path).parent)
            if folder not in self.folder_files_map:
                self.folder_files_map[folder] = set()
            self.folder_files_map[folder].add(file_path)
    
    def enrich_file_info(self, file_paths: list) -> list:
        """
        Add metadata to files using FileInfoService.
        
        Args:
            file_paths: List of file paths to enrich
            
        Returns:
            List of enriched file info dictionaries
        """
        return self.file_info_service.enrich_files(file_paths)
    
    def handle_file_removed(self, event_data):
        """
        Handle removal of a file from the list.
        
        Args:
            event_data: Event data containing file_path
        """
        file_path = event_data.get('file_path')
        if file_path in self.file_paths_list:
            # Remove from canonical list
            self.file_paths_list.remove(file_path)
            
            # Update folder mapping
            folder = str(Path(file_path).parent)
            if folder in self.folder_files_map:
                self.folder_files_map[folder].remove(file_path)
                
                # If folder is now empty, remove it
                if not self.folder_files_map[folder]:
                    del self.folder_files_map[folder]
                    
                    # Stop monitoring if needed
                    if folder in self.monitored_folders:
                        self.monitored_folders.remove(folder)
                        self.folder_monitor_service.stop_monitoring(folder)
                        
                    # Emit folder removed event
                    self.local_bus.emit(ViewEvents.FOLDER_REMOVED.value,
                                      {'folder_path': folder})
            
            # Enrich remaining files
            enriched_info = self.enrich_file_info(self.file_paths_list)
            
            # Emit event for view update
            self.local_bus.emit(ViewEvents.FILE_DISPLAY_UPDATED.value,
                              FileDisplayUpdateEvent(files=self.file_paths_list, source_path="",
                                                   enriched_info=enriched_info))
    
    def handle_folder_monitoring_toggled(self, event_data):
        """
        Handle toggling of folder monitoring.
        
        Args:
            event_data: Event data containing folder_path and enabled flag
        """
        folder_path = event_data.get('folder_path')
        enabled = event_data.get('enabled')
        
        if enabled:
            # Start monitoring
            self.monitored_folders.add(folder_path)
            self.folder_monitor_service.start_monitoring(folder_path)
        else:
            # Stop monitoring
            self.monitored_folders.remove(folder_path)
            self.folder_monitor_service.stop_monitoring(folder_path)
    
    def handle_file_discovered(self, file_path: str, folder_path: str):
        """
        Handle discovery of a new file in a monitored folder.
        
        Args:
            file_path: Path of discovered file
            folder_path: Path of folder where file was discovered
        """
        # Only add if not already in the list
        if file_path not in self.file_paths_list:
            # Add to canonical list
            self.file_paths_list.append(file_path)
            
            # Update folder mapping
            if folder_path not in self.folder_files_map:
                self.folder_files_map[folder_path] = set()
            self.folder_files_map[folder_path].add(file_path)
            
            # Enrich all files
            enriched_info = self.enrich_file_info(self.file_paths_list)
            
            # Emit event for view update
            self.local_bus.emit(ViewEvents.FILE_DISPLAY_UPDATED.value,
                              FileDisplayUpdateEvent(files=self.file_paths_list, source_path="",
                                                   enriched_info=enriched_info))
    
    def get_files(self) -> list:
        """
        Get the current list of files.
        
        Returns:
            List of file paths
        """
        return self.file_paths_list
    
    def get_folders(self) -> list:
        """
        Get the list of folders that contain files.
        
        Returns:
            List of folder paths
        """
        return list(self.folder_files_map.keys())
    
    def get_monitored_folders(self) -> list:
        """
        Get the list of folders being monitored.
        
        Returns:
            List of monitored folder paths
        """
        return list(self.monitored_folders)
    
    def clear(self):
        """Clear all files and folders."""
        self.file_paths_list = []
        self.folder_files_map = {}
        
        # Stop monitoring all folders
        for folder in self.monitored_folders:
            self.folder_monitor_service.stop_monitoring(folder)
        self.monitored_folders = set()
        
        # Emit event for view update
        self.local_bus.emit(ViewEvents.FILE_DISPLAY_UPDATED.value,
                          FileDisplayUpdateEvent(files=[], source_path="", enriched_info=[]))
```

### 3. Enhanced View Events

```python
class ViewEvents(Enum):
    """Local events for Update Data module."""
    
    # Existing events
    FILE_DISPLAY_UPDATED = "file_display_updated"
    SOURCE_DISCOVERED = "source_discovered"
    FOLDER_REMOVED = "folder_removed"
    
    # New events for file operations
    FILE_REMOVED = "file_removed"
    FOLDER_MONITORING_TOGGLED = "folder_monitoring_toggled"
```

### 4. Event Data Classes

```python
@dataclass
class FileDisplayUpdateEvent:
    """Event data for file display updates."""
    
    files: List[str]
    source_path: str = ""
    enriched_info: List[Dict[str, Any]] = None


@dataclass
class FileRemovedEvent:
    """Event data for file removal."""
    
    file_path: str


@dataclass
class FolderMonitoringToggledEvent:
    """Event data for folder monitoring toggle."""
    
    folder_path: str
    enabled: bool
```

### 5. FileManager Refactored

```python
class FileManager:
    """
    Manages file/folder selection dialogs and save location logic.
    
    Delegates file list management to FileListManager.
    """
    
    def __init__(self, view, state_manager, file_list_manager, local_bus, info_bar_service):
        """
        Initialize the file manager.
        
        Args:
            view: The view interface
            state_manager: State manager for module state
            file_list_manager: Manager for file list operations
            local_bus: Local event bus for module events
            info_bar_service: Service for info bar messages
        """
        self.view = view
        self.state_manager = state_manager
        self.state = state_manager.state
        self.file_list_manager = file_list_manager
        self.local_bus = local_bus
        self.info_bar_service = info_bar_service
        
        # Track selected source for "same as source" functionality
        self.selected_source = None
    
    def handle_source_select(self, selection_type):
        """
        Handle source selection (files or folder).
        
        Args:
            selection_type: Type of selection ('files' or 'folder')
        """
        if selection_type == 'files':
            self._select_files()
        elif selection_type == 'folder':
            self._select_folder()
    
    def _select_files(self):
        """Select individual files using file dialog."""
        try:
            # Show file dialog through view interface
            file_paths = self.view.show_files_dialog(
                "Select CSV files",
                self.state.last_directory or str(Path.home())
            )
            
            if file_paths:
                # Update state
                self.state.selected_files = file_paths
                self.state.source_type = 'files'
                self.selected_source = file_paths
                
                # Remember last directory
                if file_paths:
                    self.state.last_directory = str(Path(file_paths[0]).parent)
                
                # Delegate to file list manager
                self.file_list_manager.set_files(file_paths)
                
                # Update can_process flag
                self.state.update_can_process()
                
                log.debug(f"Selected {len(file_paths)} files")
        
        except Exception as e:
            log.error(f"Error selecting files: {e}")
            self.info_bar_service.show_error(f"Error selecting files: {str(e)}")
    
    def _select_folder(self):
        """Select folder and discover CSV files."""
        try:
            # Show folder dialog through view interface
            folder_path = self.view.show_folder_dialog(
                "Select folder containing CSV files",
                self.state.last_directory or str(Path.home())
            )
            
            if folder_path:
                # Update state
                self.state.selected_folder = folder_path
                self.state.source_type = 'folder'
                self.selected_source = folder_path
                
                # Remember last directory
                self.state.last_directory = folder_path
                
                # Discover CSV files
                file_paths = self._discover_csv_files(folder_path)
                
                if file_paths:
                    # Delegate to file list manager
                    self.file_list_manager.set_files(file_paths, folder_path)
                    
                    # Update can_process flag
                    self.state.update_can_process()
                    
                    # Emit source discovered event
                    self.local_bus.emit(ViewEvents.SOURCE_DISCOVERED.value,
                                      {'source_path': folder_path, 'file_count': len(file_paths)})
                    
                    log.debug(f"Discovered {len(file_paths)} CSV files in {folder_path}")
                else:
                    self.info_bar_service.show_warning(f"No CSV files found in {folder_path}")
        
        except Exception as e:
            log.error(f"Error selecting folder: {e}")
            self.info_bar_service.show_error(f"Error selecting folder: {str(e)}")
```

### 6. UpdateDataPresenter Integration

```python
class UpdateDataPresenter:
    """
    Presenter for Update Data module.
    
    Coordinates specialized managers and handles high-level flow.
    """
    
    def __init__(self, view):
        """
        Initialize the presenter.
        
        Args:
            view: The view interface
        """
        self.view = view
        
        # Initialize services
        self.info_bar_service = InfoBarService(view.info_bar)
        self.folder_monitor_service = FolderMonitorService()
        self.file_info_service = FileInfoService()
        
        # Initialize local event bus reference
        from ..services.local_event_bus import update_data_local_bus
        self.local_bus = update_data_local_bus
        
        # Initialize state manager
        self.state_manager = StateManager(view, self.info_bar_service)
        
        # Initialize file list manager
        self.file_list_manager = FileListManager(
            self.local_bus,
            self.folder_monitor_service,
            self.file_info_service
        )
        
        # Initialize specialized managers
        self.file_manager = FileManager(
            view,
            self.state_manager,
            self.file_list_manager,
            self.local_bus,
            self.info_bar_service
        )
        
        # ... other managers ...
        
        # Connect signals and subscribe to events
        self._connect_signals()
        self._subscribe_to_events()
    
    def _connect_signals(self):
        """Connect view signals to handlers."""
        # File operations
        self.view.source_select_requested.connect(self.file_manager.handle_source_select)
        self.view.save_select_requested.connect(self.file_manager.handle_save_select)
        
        # Connect file operation signals to event emissions
        self.view.file_removed.connect(
            lambda file_path: self.local_bus.emit(
                ViewEvents.FILE_REMOVED.value,
                FileRemovedEvent(file_path=file_path)
            )
        )
        
        self.view.folder_monitoring_toggled.connect(
            lambda folder_path, enabled: self.local_bus.emit(
                ViewEvents.FOLDER_MONITORING_TOGGLED.value,
                FolderMonitoringToggledEvent(folder_path=folder_path, enabled=enabled)
            )
        )
        
        # Process button
        self.view.process_clicked.connect(self.handle_process_clicked)
        
        # ... other signals ...
    
    def _subscribe_to_events(self):
        """Subscribe to local events."""
        # Subscribe to file display updates
        self.local_bus.subscribe(ViewEvents.FILE_DISPLAY_UPDATED.value, self.view.update_files_display)
        
        # ... other subscriptions ...
    
    def handle_process_clicked(self):
        """Handle process button click."""
        # Get current files from file list manager
        files_to_process = self.file_list_manager.get_files()
        
        # ... process files ...
```

### 7. View Implementation

```python
class UpdateDataView(QWidget, IUpdateDataView):
    """
    Concrete implementation of the Update Data view.
    
    Implements the IUpdateDataView interface and connects to Qt widgets.
    """
    
    # Implement interface signals
    cancel_clicked = Signal()
    source_select_requested = Signal(str)
    save_select_requested = Signal()
    source_option_changed = Signal(str)
    save_option_changed = Signal(str)
    process_clicked = Signal()
    file_removed = Signal(str)
    folder_monitoring_toggled = Signal(str, bool)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._setup_ui()
        self._connect_signals()
    
    def _setup_ui(self):
        # ... UI setup ...
        
        # File pane setup
        self.file_pane = FilePane()
        
        # ... more UI setup ...
    
    def _connect_signals(self):
        # Connect internal widget signals to interface signals
        
        # Connect file pane signals
        self.file_pane.file_removed.connect(self.file_removed)
        self.file_pane.folder_monitoring_toggled.connect(self.folder_monitoring_toggled)
        
        # ... other connections ...
    
    def update_files_display(self, event_data):
        """
        Update the files display with the given files.
        
        Args:
            event_data: Event data containing files and source_path
        """
        files = event_data.get('files', [])
        source_path = event_data.get('source_path', "")
        enriched_info = event_data.get('enriched_info')
        
        # Update file pane
        self.file_pane.set_files(files, source_path)
        
        # If enriched info is provided, display it
        if enriched_info:
            self.display_enriched_file_info(enriched_info)
    
    def display_enriched_file_info(self, file_info_list):
        """
        Display enriched file information.
        
        Args:
            file_info_list: List of enriched file info dictionaries
        """
        self.file_pane.set_enriched_info(file_info_list)
    
    def get_current_files(self):
        """
        Get the current list of files from the view.
        
        Returns:
            List of file paths
        """
        return self.file_pane.get_files()
    
    # ... other interface methods ...
```

## Interaction Flow

1. **Initialization**:
   - UpdateDataPresenter creates FileListManager
   - UpdateDataPresenter creates FileManager with FileListManager reference
   - UpdateDataPresenter connects view signals to event emissions

2. **File Selection**:
   - User selects files via UI
   - FileManager handles dialog interaction through view interface
   - FileManager delegates to FileListManager.set_files()
   - FileListManager emits FILE_DISPLAY_UPDATED event
   - View updates based on event subscription

3. **File Operations**:
   - User removes a file in the UI
   - View emits file_removed signal
   - Presenter converts signal to FILE_REMOVED event
   - FileListManager handles event and updates its internal list
   - FileListManager emits FILE_DISPLAY_UPDATED event
   - View updates based on event subscription

4. **Processing**:
   - When process button is clicked, presenter gets files from FileListManager
   - Presenter uses these files for processing

## Pros and Cons

### Pros

1. **Respects Architectural Boundaries**:
   - Uses view interface properly
   - No direct Qt widget coupling
   - Uses event system for communication

2. **Clear Separation of Concerns**:
   - FileListManager handles file list operations
   - FileManager focuses on dialog interactions
   - Presenter coordinates high-level flow
   - View handles UI updates

3. **Single Source of Truth**:
   - FileListManager maintains the canonical file list
   - No duplication of file list state

4. **Clean Architecture**:
   - Follows established architectural principles
   - Consistent with existing patterns

### Cons

1. **Event Overhead**:
   - More events to manage
   - Slightly more complex event flow

2. **Implementation Effort**:
   - Requires updating the view interface
   - Needs careful coordination during transition

## Implementation Steps

1. **Update View Interface**:
   - Add file operation signals if not already present

2. **Create FileListManager Class**:
   - Implement all file list operations
   - Subscribe to relevant events

3. **Update Event Definitions**:
   - Add new events for file operations
   - Create event data classes

4. **Refactor FileManager**:
   - Remove file list management code
   - Delegate to FileListManager

5. **Update UpdateDataPresenter**:
   - Create and inject FileListManager
   - Connect signals to event emissions
   - Update event subscriptions

## Conclusion

This Interface-Respecting FileListManager approach provides a clean solution that:

1. **Maintains Architectural Integrity**: Respects view interface and event system
2. **Provides Clear Separation**: Dedicated component for file list management
3. **Avoids Qt Coupling**: No direct widget access
4. **Supports Requirements**: Handles file list, folder tracking, and monitoring

This approach aligns with the established architectural principles while addressing the specific requirements for file list management.
