


# FILE PANE ARCHITECTURAL BLUEPRINT - CURRENT IMPLEMENTATION

**Status**: ✅ FIXED - All critical issues resolved
**Date**: August 1, 2025
**Purpose**: Definitive architectural guide for file pane system - blueprint for all modules

## SYSTEM OVERVIEW

The file pane system now works correctly with proper signal connections, platform compatibility, and file information display. This serves as the architectural blueprint for similar components throughout the app.

## CORE ARCHITECTURE PRINCIPLES

### 1. **Clear Signal Flow Pattern**
```
User Action → FileDisplayWidget → FileBrowser → FilePane → Presenter → FileListManager → Events → UI Update
```

### 2. **Communication Delineation**
- **Qt Signals**: Internal widget communication (same component)
- **Interface Methods**: Presenter commands to view (cross-component)
- **Events**: State notifications and logging (module-wide)

### 3. **File Information Display**
File information is displayed **NEXT TO THE FILE NAME** in tree columns:
- Column 0: File name
- Column 1: File size
- Column 2: File format/status (e.g., "ANZ Standard", "Westpac Basic")

## CURRENT IMPLEMENTATION STATUS

### ✅ **FIXED: Signal Connections**
All file operation signals are now properly connected in `ud_presenter.py`:

```python
# Connect file pane signals to FileListManager
if hasattr(self.view, 'center_display') and hasattr(self.view.center_display, 'file_pane'):
    file_pane = self.view.center_display.file_pane

    # File removal
    file_pane.publish_file_removed.connect(
        lambda file_path: self.file_list_manager.remove_file(file_path)
    )

    # File addition
    file_pane.publish_files_added.connect(
        lambda files: self.file_list_manager.add_files(files)
    )

    # Add files request
    file_pane.publish_add_files_requested.connect(
        self._handle_add_files_request
    )
```

### ✅ **FIXED: Platform Compatibility**
"Show in Finder" now works on Windows, macOS, and Linux:

```python
def _show_in_finder(self):
    system = platform.system()
    if system == "Darwin":  # macOS
        subprocess.run(['open', '-R', file_path])
    elif system == "Windows":
        subprocess.run(['explorer', '/select,', file_path])
    elif system == "Linux":
        subprocess.run(['xdg-open', os.path.dirname(file_path)])
```

### ✅ **FIXED: File Information Display**
File information is now displayed in tree columns using enriched data:

```python
# FilePane passes enriched info directly
def display_enriched_file_info(self, file_info_list):
    self.file_browser.set_enriched_files(file_info_list, source_dir)

# FileBrowser preserves enriched information
def set_enriched_files(self, file_info_list: list, source_dir: str = ""):
    self.file_display.set_enriched_files(file_info_list, source_dir)

# FileDisplayWidget shows enriched info in columns
def _add_enriched_file_item(self, file_path: str, file_info: dict, source_dir: str):
    file_item.setText(1, file_info.get('size_str', 'N/A'))
    status = f"{bank_type} {format_type}" if bank_type != 'Unknown' else "Unrecognized"
    file_item.setText(2, status)
```

## COMPONENT HIERARCHY

```
FilePane (BasePane)
├── FileBrowser (QWidget)
│   └── FileDisplayWidget (QWidget)
│       ├── QTreeWidget (file display)
│       └── Action Buttons (Add/Remove)
├── QLabel (files count)
└── QCheckBox (folder monitoring)
```

## SIGNAL ARCHITECTURE

### Internal Signals (Qt)
```python
# FileDisplayWidget → FileBrowser
file_removed = Signal(str)
file_selected = Signal(str)
add_files_requested = Signal()
files_added = Signal(list)

# FileBrowser → FilePane (republished as publish_*)
publish_file_removed = Signal(str)
publish_file_selected = Signal(str)
publish_add_files_requested = Signal()
publish_files_added = Signal(list)
```

### External Connections (Presenter)
```python
# FilePane → FileListManager (via presenter)
file_pane.publish_file_removed.connect(file_list_manager.remove_file)
file_pane.publish_files_added.connect(file_list_manager.add_files)
file_pane.publish_add_files_requested.connect(presenter._handle_add_files_request)
```

### Event Flow (Module-wide)
```python
# FileListManager → FilePane (via events)
local_bus.emit(ViewEvents.FILE_LIST_UPDATED.value, event_data)
# FilePane subscribes and updates UI accordingly
```

## INTERFACE METHODS

### FilePane Public Interface
```python
def set_files(files: list, source_dir: str = "")           # Basic file display
def display_enriched_file_info(file_info_list)            # Enriched file display
def get_files() -> list[str]                               # Get current files
def clear()                                                # Clear display
def connect_to_file_list_manager(local_bus)               # Event subscription
```

### FileBrowser Public Interface
```python
def set_files(files: list, source_dir: str = "")          # Basic file display
def set_enriched_files(file_info_list, source_dir: str)   # Enriched file display
def get_files() -> list[str]                               # Get current files
```

## BLUEPRINT FOR OTHER MODULES

### 1. **Component Structure**
- Use BasePane for top-level components
- Wrap complex widgets in intermediate classes (like FileBrowser)
- Keep actual UI widgets (QTreeWidget) at the lowest level

### 2. **Signal Pattern**
- Internal signals use Qt Signal/Slot mechanism
- Republish internal signals as `publish_*` signals at component boundaries
- Connect `publish_*` signals to business logic in presenter
- Use events for cross-component state notifications

### 3. **Data Flow**
- Accept enriched data at the highest level possible
- Pass enriched data down through the component hierarchy
- Display enriched data directly in UI widgets
- Avoid re-processing data that's already been enriched

### 4. **Interface Design**
- Provide both basic and enriched data methods
- Keep interfaces simple and focused
- Use clear, descriptive method names
- Document expected data formats

## TESTING CHECKLIST

### File Operations
- [ ] Add files via button works
- [ ] Remove files via context menu works
- [ ] Remove files via keyboard (Delete/Backspace) works
- [ ] Show in Finder/Explorer works on all platforms

### File Display
- [ ] File names display correctly
- [ ] File sizes display correctly
- [ ] File format information displays in status column
- [ ] Folder structure displays correctly in tree

### Signal Flow
- [ ] File operations update FileListManager
- [ ] FileListManager events update UI
- [ ] Folder monitoring toggle works
- [ ] No console errors during operations

## MAINTENANCE GUIDELINES

### When Adding New File Operations
1. Add signal to FileDisplayWidget
2. Republish signal in FileBrowser as `publish_*`
3. Republish signal in FilePane as `publish_*`
4. Connect signal in presenter to appropriate manager
5. Test end-to-end functionality

### When Modifying File Display
1. Update `_add_enriched_file_item` method for new columns
2. Ensure enriched data flows through all levels
3. Test with various file types
4. Verify column sizing and display

### When Extending to Other Modules
1. Copy the component hierarchy pattern
2. Adapt signal names to module context
3. Connect to appropriate module managers
4. Follow the same testing checklist

This architecture provides a clean, maintainable, and repeatable pattern for file management components throughout the application.