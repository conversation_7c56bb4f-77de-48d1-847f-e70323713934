


# ANALYSE THESE FILES:
```
flatmate\src\fm\modules\update_data\UD_REFACTORING_docs_workspace\FILE_PANE\FILE_IO_list_management_and_ui_implementation\TEST_NOTES_AND_ANALYSIS\architecture_questions_analysis.md  
flatmate\src\fm\modules\update_data\UD_REFACTORING_docs_workspace\FILE_PANE\FILE_IO_list_management_and_ui_implementation\TEST_NOTES_AND_ANALYSIS\immediate_action_plan.md  
flatmate\src\fm\modules\update_data\UD_REFACTORING_docs_workspace\FILE_PANE\FILE_IO_list_management_and_ui_implementation\TEST_NOTES_AND_ANALYSIS\issues_analysis_and_solutions.md
```

---

Consider the wider pattern in the UI related code in `ud_data`.

I want this thing working as expected.  
We have lsopy boundaries and unclear solutions.

**How should signals be handled?**  
Should they just be converted at source so that after that point we use our own system?  
I want a clear, consistent system in the app.  
This is doing my fucking head in.

Each file should be given to the browser to display with its information.  
It is **NOT** working correctly.  
The data should be displayed in a file info column in the browser.

The file pane, or the file browser widget, should present a facade with simple methods.  
I want this complexity contained.

**HOW SHOULD THIS BE ORGANISED!?**  
I want sensible, clean, maintainable architecture.  
I want this system f'ing rationalised.  
I'm sick of this slop, inconsistency, and ambiguity.

We need a clean, maintainable, sensible system that is repeatable throughout the app.  
A blueprint for all modules.

---

The most up-to-date guide we have is here:  
```
flatmate\src\fm\modules\update_data\UD_REFACTORING_docs_workspace\FILE_PANE\FILE_IO_list_management_and_ui_implementation\TEST_NOTES_AND_ANALYSIS\ARCHITECTURAL_CONSIDERATIONS
```