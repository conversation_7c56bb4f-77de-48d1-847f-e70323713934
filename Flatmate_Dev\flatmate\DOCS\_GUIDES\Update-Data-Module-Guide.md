# Update Data Module - Developer Onboarding Guide

**Last Updated**: Friday, August 1, 2025 @ 12:09:06 AM
**Module Version**: Decomposed Architecture (Post-Refactoring)  
**Target Audience**: AI Agents & Developers

## Overview

The Update Data module handles financial statement processing and database updates. It has been refactored from a monolithic presenter into a decomposed architecture with specialized managers.
`>>` = PM comments
# always check actual files before planning or refactoring! 

## Architecture Summary

### Core Pattern: Decomposed MVP
- **Model**: Database services, file processing pipeline
- **View**: Component-based UI with interface abstraction  
- **Presenter**: Coordinator + 5 specialized managers

### Key Principle: Single Responsibility
Each manager handles one specific concern, enabling independent testing and maintenance.

## Directory Structure

```
update_data/
├── __init__.py                    # Module exports
├── ud_presenter.py               # Main coordinator (328 lines)
├── ud_view.py                    # View implementation
├── interface/                    # View abstraction layer
│   └── i_view_interface.py      # IUpdateDataView protocol
├── _presenter/                   # Decomposed managers
│   ├── state_manager.py         # Consolidated state + UI sync (197 lines)
│   ├── file_manager.py          # File/folder selection + save location (396 lines)
│   └── processing_manager.py    # File processing (264 lines)
├── _view/                       # UI components
│   ├── center_panel.py          # Main content area
│   ├── left_panel.py           # >>ACTIONS/options
│   └── right_panel.py          # >>SettingsNOt implemented currently unused ...
├── config/                      # Module configuration
│   ├── ud_config.py            # Config wrapper
│   ├── ud_keys.py              # Type-safe keys
│   ├── option_types.py         # Enums
│   └── defaults.yaml           # Default values
├── services/                    # Module services
│   ├── events.py               # Event definitions
│   ├── events_data.py          # Event dataclasses
│   ├── local_event_bus.py      # Local event system
│   └── file_info_service.py    # File enrichment
└── pipeline/                    # Data processing
    ├── dw_director.py          # Processing orchestrator
    ├── dw_pipeline.py          # Core processing functions
    └── statement_handlers/     # Bank-specific parsers
```

## Presenter Architecture (Decomposed)

### 1. UpdateDataPresenter (Main Coordinator)
**Purpose**: Manager orchestration and lifecycle management
**Key Responsibilities**:
- Manager instantiation with dependency injection
- Signal routing to appropriate managers
- Module lifecycle (setup, cleanup, transitions)

**Critical Methods**:
```python
def _connect_signals(self):
    # Creates all managers with proper dependencies
    # Routes view signals to appropriate managers

def _refresh_content(self, **params):
    # Called when module becomes visible
    # Initializes UI state and configuration
```

### 2. StateManager (Consolidated State + UI Sync)
**Purpose**: Centralized state management and UI synchronization
**Key Responsibilities**:
- All presenter state (source, destination, processing status)
- State validation and updates
- State-to-view synchronization
- Guide pane contextual updates
- Widget state management

**State Properties**:
```python
@dataclass
class UpdateDataState:
    # Source configuration
    source_configured: bool = False
    source_type: str = ""  # "folder", "files", "auto_import"
    selected_files: List[str] = field(default_factory=list)

    # Destination configuration
    destination_configured: bool = False
    save_path: str = ""

    # Processing state
    can_process: bool = False
    processing: bool = False
```

**Critical Methods**:
```python
def sync_state_to_view(self):
    # MVP pattern: Presenter state → View display
    # Updates all UI elements based on current state

def update_guide_pane(self):
    # Contextual guidance based on current workflow state
    # Shows folder monitoring options when appropriate
```

### 3. FileManager (Consolidated File Operations)
**Purpose**: File/folder selection, save location, and folder monitoring
**Key Responsibilities**:
- File/folder selection dialogs
- File information enrichment via FileInfoService
- Folder monitoring integration
- Save location selection and "same as source" logic
- Maintains canonical `file_paths_list`

**Key Methods**:
```python
def handle_source_select(self, selection_type):
    # Handles "folder" or "files" selection
    # Enriches file info and updates state
    # Manages canonical file list

def enrich_file_info(self, file_paths):
    # Uses FileInfoService to add metadata
    # Returns enriched file data for display

def handle_save_select(self):
    # Save location selection
    # "Same as source" logic integration
```

### 4. ProcessingManager (File Processing & Events)
**Purpose**: File processing execution and event handling  
**Key Responsibilities**:
- Process button handling and validation
- Job sheet creation and execution via dw_director
- Processing event handling (started, stats, completed)
- Error handling and user feedback

**Event Handlers**:
```python
def on_processing_started(self, job_sheet):
def on_processing_stats(self, stats):
def on_unrecognized_files(self, unrecognized_files):
def on_processing_completed(self, result):
```

## View Architecture

### Interface Abstraction (IUpdateDataView)
**Purpose**: Break circular dependencies between presenter and view  
**Pattern**: Protocol-based interface that presenter depends on

```python
class IUpdateDataView(Protocol):
    # Signals for presenter communication
    cancel_clicked: Signal
    source_select_requested: Signal(str)
    process_clicked: Signal()
    
    # Methods for presenter to update view
    def set_process_button_text(self, text: str): ...
    def show_error(self, message: str): ...
```

### Component-Based UI
- **CenterPanelManager**: File display, processing status
- **LeftPanelManager**: Source/destination options, action buttons
- **Guide Pane**: Contextual help and folder monitoring options

## Configuration System

### Type-Safe Configuration
```python
# ud_keys.py - Hierarchical key structure
class UpdateDataKeys:
    class Paths:
        LAST_SOURCE_DIR = "paths.last_source_dir"
        LAST_SAVE_DIR = "paths.last_save_dir"
    
    class Source:
        LAST_SOURCE_OPTION = "source.last_source_option"
```

### Module-Specific Wrapper
```python
# ud_config.py - Clean interface to core config
class UpdateDataConfig(BaseLocalConfig[UpdateDataKeys]):
    def get_defaults_file_path(self) -> Path:
        return Path(__file__).parent / "defaults.yaml"
```

## Event System

### Local Event Bus
**Purpose**: Decoupled communication within module  
**Usage**: Manager-to-manager communication, view updates

```python
# Event emission
self.local_bus.emit(ViewEvents.SOURCE_DISCOVERED.value, source_data)

# Event subscription  
self.local_bus.subscribe(ViewEvents.PROCESSING_STARTED.value, self.handler)
```

### Global Event Bus Integration
**Purpose**: Cross-module communication and core service integration  
**Usage**: Database updates, folder monitoring, module transitions

## Data Processing Pipeline

### Director Pattern (dw_director.py)
**Purpose**: Orchestrate file processing workflow  
**Process**:
1. File validation and handler selection
2. Data extraction and transformation
3. Backup creation and database updates
4. Event emission for progress tracking

### Statement Handlers
**Purpose**: Bank-specific file parsing  
**Pattern**: Registry-based handler selection by file characteristics

## Development Guidelines

### Adding New Features

1. **Identify Responsibility**: Which manager should handle the new feature?
2. **Update State**: Add necessary state properties to StateManager
3. **Implement Logic**: Add methods to appropriate manager
4. **Update UI**: Modify view interface and implementation
5. **Add Configuration**: Update keys, defaults, and config wrapper
6. **Test Integration**: Ensure manager coordination works

### Manager Communication (Current Technical Debt)

**Current Pattern** (needs refactoring):
```python
# Method wrapping in presenter - ANTI-PATTERN
original_method = self.source_manager.handle_source_select
def enhanced_method(selection_type):
    original_method(selection_type)
    self.archive_manager.set_selected_source(self.source_manager.selected_source)
```

**Recommended Pattern** (future refactoring):
```python
# Event-driven communication
self.source_manager.emit('source_selected', source_data)
self.archive_manager.subscribe('source_selected', self.on_source_selected)
```

### Testing Strategy

1. **Unit Tests**: Each manager independently
2. **Integration Tests**: Manager coordination
3. **UI Tests**: View interface compliance
4. **Pipeline Tests**: File processing workflows

## Common Patterns

### Dependency Injection
```python
# In presenter _connect_signals()
self.source_manager = SourceManager(
    self.view,
    self.state, 
    self.widget_state_manager,
    self.folder_monitor_service,
    self.local_bus,
    self.info_bar_service
)
```

### State Updates
```python
# Always update state first, then sync to view
self.state.source_configured = True
self.state.update_can_process()
self.widget_state_manager.sync_state_to_view()
```

### Error Handling
```python
# Use view interface for user feedback
if not validation_result:
    self.view.show_error("Validation failed: " + error_message)
    return
```

## Migration Notes

### From Monolithic to Decomposed
- Original presenter was 352 lines handling all concerns
- Now 3 focused classes: StateManager (197 lines), FileManager (396 lines), ProcessingManager (264 lines)
- Consolidated architecture reduces complexity while maintaining separation of concerns
- Improved testability and maintainability

### Current Architecture Status
- **StateManager**: Consolidated state and UI synchronization
- **FileManager**: Consolidated file operations and save location management
- **ProcessingManager**: Focused on file processing pipeline
- **Recent Issues**: File add/remove operations need improvement (see FileListManager proposal)

### Technical Debt
1. **File list management**: Mixed responsibilities in FileManager need separation
2. **Add/remove operations**: Recent issues with file list consistency
3. **Interface bypass**: Direct calls to view components bypass interface abstraction

## Quick Reference

### Key Files to Modify
- **New feature logic**: Appropriate manager in `_presenter/`
- **UI changes**: View components in `_view/`
- **Configuration**: `config/ud_keys.py` and `defaults.yaml`
- **Events**: `services/events_data.py`

### Common Tasks
- **Add new source type**: Extend SourceManager and SourceOptions enum
- **Add processing step**: Modify ProcessingManager and pipeline
- **Add UI element**: Update view interface and implementation
- **Add configuration**: Update keys, defaults, and manager usage

---
**Status**: Functional with identified technical debt  
**Next Phase**: Event-driven manager communication refactoring
