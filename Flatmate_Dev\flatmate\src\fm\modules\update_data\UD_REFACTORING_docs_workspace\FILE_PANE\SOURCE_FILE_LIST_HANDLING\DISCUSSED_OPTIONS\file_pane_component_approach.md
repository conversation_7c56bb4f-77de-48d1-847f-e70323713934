# Component-Oriented Approach: FilePaneManager

## Concept Overview

This approach recognizes the FilePane as a mini-view with its own distinct responsibilities:

1. Create a dedicated `FilePaneManager` that handles the specific concerns of the FilePane
2. Keep `FileManager` focused on dialog interactions and high-level coordination
3. Maintain clear ownership of the file list within the `FilePaneManager`
4. Support folder monitoring and other specific requirements

## Key Insights

- FilePane is essentially a mini-view with its own responsibilities
- File selection should be handled by specialized components, not state
- We need to maintain both a list of files and knowledge of which folders have been added
- Folder monitoring is tied to specific folders that have been selected
- The main presenter primarily cares about which files to process when the button is clicked

## Implementation Design

### 1. FilePaneManager Class

```python
class FilePaneManager:
    """
    Manages the file pane component and its operations.
    
    Responsibilities:
    - Maintains the canonical list of files
    - Tracks folders that have been added
    - Handles file/folder addition and removal
    - Manages folder monitoring state
    - Updates the file pane view
    """
    
    def __init__(self, view_interface, local_bus, folder_monitor_service, file_info_service):
        """
        Initialize the file pane manager.
        
        Args:
            view_interface: The view interface for accessing the file pane
            local_bus: Local event bus for module events
            folder_monitor_service: Service for folder monitoring
            file_info_service: Service for enriching file information
        """
        self.view = view_interface
        self.local_bus = local_bus
        self.folder_monitor_service = folder_monitor_service
        self.file_info_service = file_info_service
        
        # Core data structures
        self.file_paths_list = []  # Canonical list of files
        self.folder_files_map = {}  # {folder_path: set(file_paths)}
        self.monitored_folders = set()  # Set of folders being monitored
        
        # Connect to file pane signals
        self._connect_signals()
    
    def _connect_signals(self):
        """Connect to signals from the file pane view."""
        # File operations
        self.view.file_pane.file_removed.connect(self.handle_file_removed)
        self.view.file_pane.folder_monitoring_toggled.connect(self.handle_folder_monitoring_toggled)
        
        # Subscribe to folder monitor events
        self.folder_monitor_service.file_discovered.connect(self.handle_file_discovered)
    
    def set_files(self, files: list, source_path: str = ""):
        """
        Set the list of files and update the view.
        
        Args:
            files: List of file paths
            source_path: Source directory for relative paths
        """
        # Update canonical list
        self.file_paths_list = files
        
        # Update folder mapping
        self._track_files_by_folder(files)
        
        # Enrich file info
        enriched_info = self.enrich_file_info(files)
        
        # Update view
        self.view.display_enriched_file_info(enriched_info)
        
        # Emit event for other components
        self.local_bus.emit(ViewEvents.FILE_DISPLAY_UPDATED.value,
                          FileDisplayUpdateEvent(files=files, source_path=source_path))
    
    def _track_files_by_folder(self, files: list):
        """
        Group files by their parent folders.
        
        Args:
            files: List of file paths
        """
        # Reset folder mapping if this is a new selection
        if not set(files).issubset(set(self.file_paths_list)):
            self.folder_files_map = {}
            
        # Build folder mapping
        for file_path in files:
            folder = str(Path(file_path).parent)
            if folder not in self.folder_files_map:
                self.folder_files_map[folder] = set()
            self.folder_files_map[folder].add(file_path)
    
    def enrich_file_info(self, file_paths: list) -> list:
        """
        Add metadata to files using FileInfoService.
        
        Args:
            file_paths: List of file paths to enrich
            
        Returns:
            List of enriched file info dictionaries
        """
        return self.file_info_service.enrich_files(file_paths)
    
    def handle_file_removed(self, file_path: str):
        """
        Handle removal of a file from the list.
        
        Args:
            file_path: Path of file to remove
        """
        if file_path in self.file_paths_list:
            # Remove from canonical list
            self.file_paths_list.remove(file_path)
            
            # Update folder mapping
            folder = str(Path(file_path).parent)
            if folder in self.folder_files_map:
                self.folder_files_map[folder].remove(file_path)
                
                # If folder is now empty, remove it
                if not self.folder_files_map[folder]:
                    del self.folder_files_map[folder]
                    
                    # Stop monitoring if needed
                    if folder in self.monitored_folders:
                        self.monitored_folders.remove(folder)
                        self.folder_monitor_service.stop_monitoring(folder)
                        
                    # Emit folder removed event
                    self.local_bus.emit(ViewEvents.FOLDER_REMOVED.value,
                                      {'folder_path': folder})
            
            # Update view with remaining files
            enriched_info = self.enrich_file_info(self.file_paths_list)
            self.view.display_enriched_file_info(enriched_info)
            
            # Emit event for other components
            self.local_bus.emit(ViewEvents.FILE_DISPLAY_UPDATED.value,
                              FileDisplayUpdateEvent(files=self.file_paths_list, source_path=""))
    
    def handle_folder_monitoring_toggled(self, folder_path: str, enabled: bool):
        """
        Handle toggling of folder monitoring.
        
        Args:
            folder_path: Path of folder to monitor
            enabled: Whether monitoring is enabled
        """
        if enabled:
            # Start monitoring
            self.monitored_folders.add(folder_path)
            self.folder_monitor_service.start_monitoring(folder_path)
        else:
            # Stop monitoring
            self.monitored_folders.remove(folder_path)
            self.folder_monitor_service.stop_monitoring(folder_path)
    
    def handle_file_discovered(self, file_path: str, folder_path: str):
        """
        Handle discovery of a new file in a monitored folder.
        
        Args:
            file_path: Path of discovered file
            folder_path: Path of folder where file was discovered
        """
        # Only add if not already in the list
        if file_path not in self.file_paths_list:
            # Add to canonical list
            self.file_paths_list.append(file_path)
            
            # Update folder mapping
            if folder_path not in self.folder_files_map:
                self.folder_files_map[folder_path] = set()
            self.folder_files_map[folder_path].add(file_path)
            
            # Update view with all files
            enriched_info = self.enrich_file_info(self.file_paths_list)
            self.view.display_enriched_file_info(enriched_info)
            
            # Emit event for other components
            self.local_bus.emit(ViewEvents.FILE_DISPLAY_UPDATED.value,
                              FileDisplayUpdateEvent(files=self.file_paths_list, source_path=""))
    
    def get_files(self) -> list:
        """
        Get the current list of files.
        
        Returns:
            List of file paths
        """
        return self.file_paths_list
    
    def get_folders(self) -> list:
        """
        Get the list of folders that contain files.
        
        Returns:
            List of folder paths
        """
        return list(self.folder_files_map.keys())
    
    def get_monitored_folders(self) -> list:
        """
        Get the list of folders being monitored.
        
        Returns:
            List of monitored folder paths
        """
        return list(self.monitored_folders)
    
    def clear(self):
        """Clear all files and folders."""
        self.file_paths_list = []
        self.folder_files_map = {}
        
        # Stop monitoring all folders
        for folder in self.monitored_folders:
            self.folder_monitor_service.stop_monitoring(folder)
        self.monitored_folders = set()
        
        # Update view
        self.view.display_enriched_file_info([])
        
        # Emit event for other components
        self.local_bus.emit(ViewEvents.FILE_DISPLAY_UPDATED.value,
                          FileDisplayUpdateEvent(files=[], source_path=""))
```

### 2. FileManager Refactored

```python
class FileManager:
    """
    Manages file/folder selection dialogs and save location logic.
    
    Delegates file list management to FilePaneManager.
    """
    
    def __init__(self, view, state_manager, file_pane_manager, local_bus, info_bar_service):
        """
        Initialize the file manager.
        
        Args:
            view: The view interface
            state_manager: State manager for module state
            file_pane_manager: Manager for file pane operations
            local_bus: Local event bus for module events
            info_bar_service: Service for info bar messages
        """
        self.view = view
        self.state_manager = state_manager
        self.state = state_manager.state
        self.file_pane_manager = file_pane_manager
        self.local_bus = local_bus
        self.info_bar_service = info_bar_service
        
        # Track selected source for "same as source" functionality
        self.selected_source = None
    
    def handle_source_select(self, selection_type):
        """
        Handle source selection (files or folder).
        
        Args:
            selection_type: Type of selection ('files' or 'folder')
        """
        if selection_type == 'files':
            self._select_files()
        elif selection_type == 'folder':
            self._select_folder()
    
    def _select_files(self):
        """Select individual files using file dialog."""
        try:
            # Show file dialog
            file_paths = self.view.show_files_dialog(
                "Select CSV files",
                self.state.last_directory or str(Path.home())
            )
            
            if file_paths:
                # Update state
                self.state.selected_files = file_paths
                self.state.source_type = 'files'
                self.selected_source = file_paths
                
                # Remember last directory
                if file_paths:
                    self.state.last_directory = str(Path(file_paths[0]).parent)
                
                # Delegate to file pane manager
                self.file_pane_manager.set_files(file_paths)
                
                # Update can_process flag
                self.state.update_can_process()
                
                log.debug(f"Selected {len(file_paths)} files")
        
        except Exception as e:
            log.error(f"Error selecting files: {e}")
            self.info_bar_service.show_error(f"Error selecting files: {str(e)}")
    
    def _select_folder(self):
        """Select folder and discover CSV files."""
        try:
            # Show folder dialog
            folder_path = self.view.show_folder_dialog(
                "Select folder containing CSV files",
                self.state.last_directory or str(Path.home())
            )
            
            if folder_path:
                # Update state
                self.state.selected_folder = folder_path
                self.state.source_type = 'folder'
                self.selected_source = folder_path
                
                # Remember last directory
                self.state.last_directory = folder_path
                
                # Discover CSV files
                file_paths = self._discover_csv_files(folder_path)
                
                if file_paths:
                    # Delegate to file pane manager
                    self.file_pane_manager.set_files(file_paths, folder_path)
                    
                    # Update can_process flag
                    self.state.update_can_process()
                    
                    # Emit source discovered event
                    self.local_bus.emit(ViewEvents.SOURCE_DISCOVERED.value,
                                      {'source_path': folder_path, 'file_count': len(file_paths)})
                    
                    log.debug(f"Discovered {len(file_paths)} CSV files in {folder_path}")
                else:
                    self.info_bar_service.show_warning(f"No CSV files found in {folder_path}")
        
        except Exception as e:
            log.error(f"Error selecting folder: {e}")
            self.info_bar_service.show_error(f"Error selecting folder: {str(e)}")
    
    def _discover_csv_files(self, folder_path: str) -> list:
        """
        Discover CSV files in a folder.
        
        Args:
            folder_path: Path to folder to search
            
        Returns:
            List of CSV file paths
        """
        csv_files = []
        try:
            for file_path in Path(folder_path).glob("*.csv"):
                csv_files.append(str(file_path))
        except Exception as e:
            log.error(f"Error discovering CSV files: {e}")
        
        return csv_files
    
    def handle_save_select(self):
        """Handle save location selection."""
        # ... existing code ...
```

### 3. UpdateDataPresenter Integration

```python
class UpdateDataPresenter:
    """
    Presenter for Update Data module.
    
    Coordinates specialized managers and handles high-level flow.
    """
    
    def __init__(self, view):
        """
        Initialize the presenter.
        
        Args:
            view: The view interface
        """
        self.view = view
        
        # Initialize services
        self.info_bar_service = InfoBarService(view.info_bar)
        self.folder_monitor_service = FolderMonitorService()
        
        # Initialize local event bus reference
        from ..services.local_event_bus import update_data_local_bus
        self.local_bus = update_data_local_bus
        
        # Initialize state manager
        self.state_manager = StateManager(view, self.info_bar_service)
        
        # Initialize file info service
        self.file_info_service = FileInfoService()
        
        # Initialize file pane manager
        self.file_pane_manager = FilePaneManager(
            view,
            self.local_bus,
            self.folder_monitor_service,
            self.file_info_service
        )
        
        # Initialize specialized managers
        self.file_manager = FileManager(
            view,
            self.state_manager,
            self.file_pane_manager,
            self.local_bus,
            self.info_bar_service
        )
        
        # ... other managers ...
        
        # Connect signals and subscribe to events
        self._connect_signals()
        self._subscribe_to_events()
    
    def _connect_signals(self):
        """Connect view signals to handlers."""
        # File operations
        self.view.source_select_requested.connect(self.file_manager.handle_source_select)
        self.view.save_select_requested.connect(self.file_manager.handle_save_select)
        
        # Process button
        self.view.process_clicked.connect(self.handle_process_clicked)
        
        # ... other signals ...
    
    def _subscribe_to_events(self):
        """Subscribe to local events."""
        # Subscribe to file display updates
        self.local_bus.subscribe(ViewEvents.FILE_DISPLAY_UPDATED.value, self.view.update_files_display)
        
        # ... other subscriptions ...
    
    def handle_process_clicked(self):
        """Handle process button click."""
        # Get current files from file pane manager
        files_to_process = self.file_pane_manager.get_files()
        
        # ... process files ...
```

## Interaction Flow

1. **Initialization**:
   - UpdateDataPresenter creates FilePaneManager
   - UpdateDataPresenter creates FileManager with FilePaneManager reference
   - FilePaneManager connects to file pane signals

2. **File Selection**:
   - User selects files via UI
   - FileManager handles dialog interaction
   - FileManager delegates to FilePaneManager.set_files()
   - FilePaneManager updates its internal list and the view
   - FilePaneManager emits FILE_DISPLAY_UPDATED event

3. **File Operations**:
   - FilePaneManager handles file removal, folder monitoring, etc.
   - FilePaneManager maintains folder mapping and monitoring state
   - FilePaneManager updates the view directly
   - FilePaneManager emits events for other components

4. **Processing**:
   - When process button is clicked, presenter gets files from FilePaneManager
   - Presenter uses these files for processing

## Pros and Cons

### Pros

1. **Clear Separation of Concerns**:
   - FilePaneManager handles file pane specific operations
   - FileManager focuses on dialog interactions
   - Presenter coordinates high-level flow

2. **Natural Boundaries**:
   - Respects the FilePane as a mini-view with its own responsibilities
   - Aligns with UI component structure

3. **Single Source of Truth**:
   - FilePaneManager maintains the canonical file list
   - No duplication of file list state

4. **Specialized Operations**:
   - FilePaneManager provides folder-based operations
   - Supports folder monitoring and other specific requirements

5. **Clean Architecture**:
   - Follows component-oriented design
   - Clear responsibilities and ownership

### Cons

1. **Additional Component**:
   - Adds another class to the architecture
   - Requires understanding of component relationships

2. **Implementation Effort**:
   - Requires more refactoring than simpler approaches
   - Needs careful coordination during transition

## Implementation Steps

1. **Create FilePaneManager Class**:
   - Implement all file pane operations
   - Connect to file pane signals

2. **Refactor FileManager**:
   - Remove file list management code
   - Delegate to FilePaneManager

3. **Update UpdateDataPresenter**:
   - Create and inject FilePaneManager
   - Update event subscriptions

4. **Update View Components**:
   - Ensure they emit the right signals
   - Connect to FilePaneManager

## Conclusion

The Component-Oriented Approach with a dedicated FilePaneManager recognizes the FilePane as a mini-view with its own responsibilities. This approach:

1. **Respects Natural Boundaries**: Aligns with UI component structure
2. **Maintains Clear Ownership**: FilePaneManager owns the file list
3. **Supports Specialized Operations**: Folder monitoring, file grouping, etc.
4. **Follows Clean Architecture**: Clear responsibilities and dependencies

This approach provides a robust foundation for file list management while respecting the natural structure of the UI and functionality.
